import factory
from factory.faker import Faker

from api.constants import KANNA_USER_ROLE
from authentication.factories import UserFactory
from companies.factories import CompanyFactory
from kanna_api.models import Role, UserKanna
from kanna_api.models.user_kanna import LevelChoices


class RoleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Role

    name = factory.Sequence(lambda n: f"Role {n}")
    created_by = Faker("uuid4")
    updated_by = Faker("uuid4")


class UserKannaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = UserKanna

    user_uuid = Faker("uuid4")
    company_kanna_uuid = Faker("uuid4")
    company_kanna_name = factory.Sequence(lambda n: f"Company Kanna {n}")
    ka_access_token = "Token[XXXXXXXXXXXXXX]"
    ka_refresh_token = "RefreshToken[XXXXXXXXXXXXXX]"
    last_login_at = None
    created_by = Faker("uuid4")
    updated_by = Faker("uuid4")
    email = Faker("email")

    role = factory.SubFactory(RoleFactory)
    company = factory.SubFactory(CompanyFactory)
    user = factory.SubFactory(UserFactory)

    is_active = True
    level = LevelChoices.LEVEL_1
    two_auth = 0  # Default to NO_TWO_AUTH

    name = Faker("name")
    pic_position = Faker("job")
    tel = Faker("phone_number")
    address = Faker("address")

    @factory.post_generation
    def set_role_by_type(obj, create, extracted, **kwargs):
        """Set role based on extracted role type or use default"""
        if extracted:
            if extracted == "ACT":
                obj.role_id = KANNA_USER_ROLE["ACT"]
            elif extracted == "ADMIN_SYSTEM":
                obj.role_id = KANNA_USER_ROLE["ADMIN_SYSTEM"]
            elif extracted == "DEALER":
                obj.role_id = KANNA_USER_ROLE["DEALER"]
            elif extracted == "MAKEUP_SHOP":
                obj.role_id = KANNA_USER_ROLE["MAKEUP_SHOP"]
            if create:
                obj.save()


class ActUserKannaFactory(UserKannaFactory):
    """Factory for ACT role UserKanna"""

    role_id = KANNA_USER_ROLE["ACT"]
    level = LevelChoices.LEVEL_1


class AdminUserKannaFactory(UserKannaFactory):
    """Factory for Admin role UserKanna"""

    role_id = KANNA_USER_ROLE["ADMIN_SYSTEM"]
    level = None  # Admin users typically don't have levels


class DealerUserKannaFactory(UserKannaFactory):
    """Factory for Dealer role UserKanna"""

    role_id = KANNA_USER_ROLE["DEALER"]
    level = LevelChoices.LEVEL_1

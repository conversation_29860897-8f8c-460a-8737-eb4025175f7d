from django import template

register = template.Library()


def get_element_type(element):
    if isinstance(element, dict):
        return element.get("type")
    return element.type


@register.filter
def sort_steps(steps):
    order = {
        "PAINT_BOTTOM_1": 1,
        "PAINT_BOTTOM_2": 2,
        "PAINT_MIDDLE": 3,
        "PAINT_OVER_1": 4,
        "PAINT_OVER_2": 5,
    }
    return sorted(steps, key=lambda s: order.get(get_element_type(s), 999))

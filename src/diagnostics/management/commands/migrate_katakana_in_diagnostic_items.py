"""
Django management command to perform a one-time data migration on DiagnosticConditionItem model.

This command replaces half-width katakana "ｸﾘﾔｰ" with full-width katakana "クリャー"
in the title field of DiagnosticConditionItem records.

Usage:
    python manage.py migrate_katakana_in_diagnostic_items

The command is idempotent and safe to run multiple times.
"""
import logging

from django.core.management.base import BaseCommand
from django.db import transaction

from diagnostics.models import DiagnosticConditionItem

logger = logging.getLogger(__name__)


# python manage.py migrate_katakana_in_diagnostic_items
class Command(BaseCommand):
    help = "Migrate half-width katakana 'ｸﾘﾔｰ' to full-width katakana 'クリヤー' in DiagnosticConditionItem titles"

    def handle(self, *args, **options):
        """
        Main handler for the management command.
        Queries and updates DiagnosticConditionItem records.
        """
        # Define the search and replacement strings
        half_width_katakana = "ｸﾘﾔｰ"
        full_width_katakana = "クリヤー"

        self.stdout.write(
            self.style.WARNING(
                f"Starting migration: Replacing '{half_width_katakana}' with '{full_width_katakana}'"
            )
        )

        try:
            items_to_update = DiagnosticConditionItem.objects.filter(
                title__contains=half_width_katakana
            )

            total_count = items_to_update.count()
            self.stdout.write(f"Found {total_count} record(s) to update.")
            logger.info(
                f"Found {total_count} DiagnosticConditionItem record(s) containing '{half_width_katakana}'"
            )

            updated_count = 0
            with transaction.atomic():
                for item in items_to_update:
                    original_title = item.title
                    new_title = item.title.replace(
                        half_width_katakana, full_width_katakana
                    )
                    item.title = new_title
                    item.save(update_fields=["title"])
                    logger.info(
                        f"Updated DiagnosticConditionItem - "
                        f"ID: {item.id}, "
                        f"Original title: '{original_title}', "
                        f"New title: '{new_title}'"
                    )

                    self.stdout.write(
                        f"  Updated ID {item.id}: '{original_title}' -> '{new_title}'"
                    )

                    updated_count += 1

            self.stdout.write(
                self.style.SUCCESS(
                    f"\nMigration completed successfully! Updated {updated_count} record(s)."
                )
            )
            logger.info(
                f"Migration completed successfully. Updated {updated_count} DiagnosticConditionItem record(s)."
            )

        except Exception as e:
            error_message = f"Error during migration: {str(e)}"
            self.stdout.write(self.style.ERROR(error_message))
            logger.exception(error_message)
            raise

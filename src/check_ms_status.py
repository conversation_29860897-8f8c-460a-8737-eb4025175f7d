from management_sheets.models import ManagementSheet, ManagementSheetStatus
from approve_flow.models import ApproveFlowStep
from companies.models import ShopDealer
from kanna_api.models.user_kanna import UserKanna

ms = ManagementSheet.objects.get(pk=538)
print(ms.status)
ms.status = ManagementSheetStatus.WAITING_APPROVAL
ms.save()

ms.status = ManagementSheetStatus.APPROVED
ms.save()

approve_steps = ApproveFlowStep.objects.filter(management_sheet=ms).order_by('-id')
for step in approve_steps:
    if step.user:
        user = step.user.kanna_user
        print(f"Step ID: {step.id}, Status: {step.status}, Updated At: {step.updated_at}, User Email: {user.email}, user role: {user.role}")
    else:
        print(step.id, step.status, step.updated_at, "No User")


from approve_flow.services import (
    query_current_approve_flow_step_from_admin_ms
)
approve_step_id = approve_steps[0].id
last_step = query_current_approve_flow_step_from_admin_ms(ms)
print(last_step, last_step.status)

user_pic_ms = UserKanna.objects.filter(email=ms.pic.email).first()
print(user_pic_ms)  # user 17
company_dealer = ShopDealer.objects.filter(shop=user_pic_ms.company).first()
print(company_dealer)  # shop dealer 537

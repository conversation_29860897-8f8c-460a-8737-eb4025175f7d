from typing import Optional

from django.db import models
from django.utils.translation import gettext_lazy as _


class BusinessTypes(models.TextChoices):
    MANAGEMENT_SHEET = "MS"
    DIAGNOSTIC = "SDS"
    WARRANTY = "WARRANTY"


class BusinessSubTypes(models.TextChoices):
    ROOF_DIAGNOSTIC = "SDS_ROOF", _("Roof Diagnostis")
    OUTER_WALL_DIAGNOSTIC = "SDS_OUTER_WALL", _("Outer Wall Diagnostis")


class TemplateStatus(models.TextChoices):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class Template(models.Model):
    id = models.BigAutoField(primary_key=True)
    display_name = models.CharField(
        max_length=255, null=True, blank=True
    )  # name display in table
    template_name = models.CharField(
        max_length=32, null=True, blank=True
    )  # name of tempalte file store fe
    business_type = models.Char<PERSON>ield(
        max_length=255,
        choices=BusinessTypes.choices,
    )
    sub_type = models.CharField(
        max_length=255, choices=BusinessSubTypes.choices, null=True, blank=True
    )
    status = models.CharField(
        max_length=16,
        choices=TemplateStatus.choices,
        default=TemplateStatus.INACTIVE.value,
    )
    description = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    extra_fields = models.JSONField(default=dict)

    class Meta:
        db_table = "template_management"
        ordering = ["-id"]

    def get_extra_field_value(self, key: str) -> Optional[str]:
        for field_dict in self.extra_fields:
            if field_dict.get("key") == key:
                return field_dict.get("value")
        return None

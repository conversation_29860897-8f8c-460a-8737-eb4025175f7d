from django.core.management.base import BaseCommand
from template_management.models import BusinessTypes, Template

NEW_ENTERPRISE_NAME = "ＡＧＣコーテック株式会社"


# python manage.py update_warranty_pdf_config
class Command(BaseCommand):
    help = "Update the enterprise_name field in extra_fields for all warranty templates"

    def handle(self, *args, **options) -> None:
        warranty_templates = Template.objects.filter(
            business_type=BusinessTypes.WARRANTY
        )
        for template in warranty_templates:
            self._update_warranty_template(template)
            template.save()

        self.stdout.write(
            self.style.SUCCESS(f"Updated {len(warranty_templates)} template(s).")
        )

    def _update_warranty_template(self, template: Template) -> None:
        self._update_template_enterprise_name(
            template=template, enterprise_name=NEW_ENTERPRISE_NAME
        )

    def _get_extra_fields_config(self, template: Template, key: str) -> dict:
        for field_dict in template.extra_fields:
            if field_dict.get("key") == key:
                return field_dict
        return {}

    def _update_extra_fields(
        self, template: Template, key: str, new_value: str
    ) -> bool:
        for field_dict in template.extra_fields:
            if field_dict.get("key") == key:
                field_dict["value"] = new_value
                return True
        return False

    def _update_template_enterprise_name(
        self, template: Template, enterprise_name: str
    ) -> None:
        self._update_extra_fields(template, "enterprise_name", enterprise_name)
        self._add_show_residence_config(template)

    def _add_show_residence_config(self, template: Template) -> None:
        show_residence_key = "show_residence"
        show_residence_value = True
        show_residence = self._get_extra_fields_config(template, show_residence_key)
        show_residence_data = {
            "key": show_residence_key,
            "label": "住居表示",
            "type": "boolean",
            "value": show_residence_value,
        }
        if not show_residence:
            template.extra_fields.append(show_residence_data)
        else:
            self._update_extra_fields(
                template, show_residence_key, show_residence_value
            )

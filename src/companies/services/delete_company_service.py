from dataclasses import dataclass
from logging import getLogger

from django.db.models import Q

from companies.models import Company, ShopDealer
from kanna_api.models import UserKanna

logger = getLogger(__name__)


@dataclass
class DeleteCompanyService:
    company: Company
    user: <PERSON>r<PERSON><PERSON>

    def check_linked_to_approve_flow(self):
        approve_steps = self.company.company_approve_steps.exists()
        if approve_steps > 0:
            raise ValueError(f"{self.company.name} が承認フローに存在します」")

    def check_linked_to_shop_dealer(self):
        shop_dealers = ShopDealer.objects.filter(
            Q(shop=self.company) | Q(dealer=self.company)
        )
        if shop_dealers.exists():
            raise ValueError(f"{self.company.name} がShopDealerに存在します")

    def execute(self):
        self.check_linked_to_shop_dealer()
        self.check_linked_to_approve_flow()
        self.company.delete()

from dataclasses import dataclass
from logging import getLogger
from typing import Optional

from django.db.models import Q

from approve_flow_settings.models import ApproveFlowSetting
from companies.models import Company, ShopDealer
from kanna_api.models import UserKanna

logger = getLogger(__name__)


@dataclass
class DeleteCompanyService:
    company: Company

    def validate_linked_to_approve_flow_setting(self) -> Optional[str]:
        approve_flow_settings = (
            ApproveFlowSetting.objects.filter(
                (Q(shop=self.company) | Q(dealer=self.company) | Q(act=self.company))
                & Q(is_deleted=False)
            )
            .values("id")
            .order_by("id")
        )
        if approve_flow_settings:
            approve_flow_ids = [str(flow["id"]) for flow in approve_flow_settings]
            return f"承認フローに使用しています。承認フローID：{', '.join(approve_flow_ids)}"
        return None

    def validate_link_to_user(self) -> Optional[str]:
        users = (
            UserKanna.objects.filter(company=self.company).values("id").order_by("id")
        )
        if users:
            user_ids = [str(user["id"]) for user in users]
            return f"担当者で指定されています。担当者ID：{', '.join(user_ids)}"
        return None

    def delete_related_data(self):
        ShopDealer.objects.filter(
            Q(shop=self.company) | Q(dealer=self.company)
        ).delete()

    def execute(self):
        deletable_message = self.get_deletable_message()
        if deletable_message:
            raise ValueError(deletable_message)
        self.company.delete()
        self.delete_related_data()

    def get_deletable_message(self) -> Optional[str]:
        validators = [
            self.validate_linked_to_approve_flow_setting,
            self.validate_link_to_user,
        ]
        error_messages = []
        for validator in validators:
            message = validator()
            if message:
                error_messages.append(message)
        return "\n".join(error_messages) if error_messages else None

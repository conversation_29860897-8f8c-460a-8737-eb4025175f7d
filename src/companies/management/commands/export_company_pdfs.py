"""
Django management command to export PDFs for companies.

This command exports warranty, management sheet, and diagnostic PDFs for companies,
optionally uploading them to S3.

Usage:
    python manage.py export_company_pdfs
    python manage.py export_company_pdfs --type warranty
    python manage.py export_company_pdfs --company 123
    python manage.py export_company_pdfs --type diagnostics --company 123 --upload-to-s3
"""
import logging
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Optional
from urllib.parse import urlparse

from django.conf import settings
from django.core.management.base import BaseCommand, CommandParser

from companies.models import Company
from diagnostics.models import Diagnostic
from files.services import S3
from management_sheets.models import ManagementSheet
from warranty.models import Warranty

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Export PDFs for companies (warranty, management_sheet, diagnostics)"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.total_files_copied = 0

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument(
            "--type",
            type=str,
            choices=["warranty", "management_sheet", "diagnostics"],
            default=None,
            help="Type of PDF to export (default: all types)",
        )
        parser.add_argument(
            "--company",
            type=int,
            default=None,
            help="Company ID to export PDFs for (default: all companies)",
        )
        parser.add_argument(
            "--upload-to-s3",
            action="store_true",
            default=False,
            help="Upload the zip file to S3 after export",
        )

    def handle(self, *args, **options):
        """Main entry point for the command."""
        pdf_type = options.get("type")
        company_id = options.get("company")
        upload_to_s3 = options.get("upload_to_s3")

        self.__log_and_print(f"Starting PDF export process...")
        self.__log_and_print(f"Type: {pdf_type or 'all'}")
        self.__log_and_print(f"Company ID: {company_id or 'all'}")
        self.__log_and_print(f"Upload to S3: {upload_to_s3}")

        # Create a root export folder
        export_folder = self.__create_export_folder()
        self.__log_and_print(f"Created export folder: {export_folder}")

        # Load companies
        companies = self.__load_companies(company_id)
        if not companies:
            return

        self.__copy_pdf_files(
            companies=companies, export_folder=export_folder, pdf_type=pdf_type
        )

        # Zip the export folder
        zip_file_path = self.__zip_export_folder(export_folder)
        self.__log_and_print(f"Created zip file: {zip_file_path}")

        # Upload to S3 if requested
        if upload_to_s3:
            self.__upload_to_s3(zip_file_path)
            self.__log_and_print(f"Removing zip file: {zip_file_path} from local...")
            os.remove(zip_file_path)
            self.__log_and_print(f"Zip file removed successfully.")

        self.__log_and_print(
            f"Export completed. Total files copied: {self.total_files_copied}"
        )

    def __copy_pdf_files(
        self, companies: List[Company], export_folder: str, pdf_type: str
    ) -> Optional[int]:
        """Copy PDF files for a company."""
        if pdf_type == "warranty":
            self.total_files_copied = self.__copy_warranty_pdfs(
                companies, export_folder
            )
            return None
        elif pdf_type == "management_sheet":
            self.total_files_copied = self.__copy_management_sheet_pdfs(
                companies, export_folder
            )
            return None
        elif pdf_type == "diagnostics":
            self.total_files_copied = self.__copy_diagnostic_pdfs(
                companies, export_folder
            )
            return None

        # Copy all PDFs
        self.total_files_copied = self.__copy_warranty_pdfs(companies, export_folder)
        self.total_files_copied += self.__copy_management_sheet_pdfs(
            companies, export_folder
        )
        self.total_files_copied += self.__copy_diagnostic_pdfs(companies, export_folder)
        return None

    def __log_and_print(self, message: str) -> None:
        """Helper method to log and print messages."""
        logger.info(message)
        self.stderr.write(message)

    def __create_export_folder(self) -> str:
        """Create root export folder with timestamp."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        folder_name = f"export_{timestamp}"
        folder_path = os.path.join(settings.STATIC_ROOT, "pdf", folder_name)
        os.makedirs(folder_path, exist_ok=True)
        return folder_path

    def __load_companies(self, company_id: Optional[int]) -> List[Company]:
        """Load companies based on the provided ID or all companies."""
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
                self.__log_and_print(
                    f"Found company: {company.name} (ID: {company.id})"
                )
                return [company]
            except Company.DoesNotExist:
                self.__log_and_print(f"Error: Company with ID {company_id} not found")
                return []
        else:
            companies = list(Company.objects.all())
            self.__log_and_print(f"Found {len(companies)} companies")
            return companies

    def _copy_pdf_to_company_folder(
        self, pdf_url: str, company_folder: str, type_name: str
    ) -> bool:
        file_path = self.__extract_file_path_from_url(pdf_url)
        if not file_path:
            self.__log_and_print(f"PDF file not found: {pdf_url}")
            return False
        file_folder = os.path.join(company_folder, type_name)
        Path(file_folder).mkdir(parents=True, exist_ok=True)
        shutil.copy2(file_path, file_folder)
        return True

    def __copy_warranty_pdfs(self, companies: List[Company], export_folder: str) -> int:
        """Copy warranty PDFs for a company."""
        warranties = (
            Warranty.objects.filter(
                management_sheet__diagnostic_management_sheet__company__in=companies,
                pdf_url__isnull=False,
            )
            .exclude(pdf_url="")
            .prefetch_related("management_sheet__diagnostic_management_sheet__company")
        )

        copy_file_count = 0
        for warranty in warranties:
            company = (
                warranty.management_sheet.diagnostic_management_sheet.first().company
            )
            company_folder = self.__create_company_folder(export_folder, company)
            if self._copy_pdf_to_company_folder(
                warranty.pdf_url, company_folder, "warranty"
            ):
                copy_file_count += 1
        return copy_file_count

    def __copy_diagnostic_pdfs(
        self, companies: List[Company], export_folder: str
    ) -> int:
        """Copy diagnostic PDFs for a company."""
        diagnostics = (
            Diagnostic.objects.filter(company__in=companies, pdf_url__isnull=False)
            .exclude(pdf_url="")
            .select_related("company")
        )

        copy_file_count = 0
        for diagnostic in diagnostics:
            company = diagnostic.company
            company_folder = self.__create_company_folder(export_folder, company)
            if self._copy_pdf_to_company_folder(
                diagnostic.pdf_url, company_folder, "diagnostic"
            ):
                copy_file_count += 1
        return copy_file_count

    def __copy_management_sheet_pdfs(
        self, companies: List[Company], export_folder: str
    ) -> int:
        """Copy management sheet PDFs for a company."""
        management_sheets = (
            ManagementSheet.objects.filter(
                diagnostic_management_sheet__company__in=companies,
                pdf_url__isnull=False,
            )
            .exclude(pdf_url="")
            .prefetch_related("diagnostic_management_sheet__company")
        )

        copy_file_count = 0
        for management_sheet in management_sheets:
            company = management_sheet.diagnostic_management_sheet.first().company
            company_folder = self.__create_company_folder(export_folder, company)
            if self._copy_pdf_to_company_folder(
                management_sheet.pdf_url, company_folder, "management_sheet"
            ):
                copy_file_count += 1
        return copy_file_count

    def __create_company_folder(self, export_folder: str, company: Company) -> str:
        """Create a folder for a specific company."""
        folder_name = f"{company.id}_{company.name}"
        folder_path = os.path.join(export_folder, folder_name)
        os.makedirs(folder_path, exist_ok=True)
        return folder_path

    def __extract_file_path_from_url(self, pdf_url: str) -> Optional[str]:
        """Extract file path from PDF URL."""
        if not pdf_url:
            return None

        # Parse URL to get a path
        parsed_url = urlparse(pdf_url)
        url_path = parsed_url.path

        # Remove the leading slash and 'static/' prefix if present
        if url_path.startswith("/"):
            url_path = url_path[1:]
        if url_path.startswith("static/"):
            url_path = url_path[7:]  # Remove 'static/' prefix

        # Construct a full file path
        file_path = os.path.join(settings.STATIC_ROOT, url_path)

        return file_path if os.path.exists(file_path) else None

    def __zip_export_folder(self, export_folder: str) -> str:
        """
        Zip the export folder.

        Returns:
            Path to the created zip file
        """
        zip_file_path = f"{export_folder}.zip"
        shutil.make_archive(export_folder, "zip", export_folder)
        shutil.rmtree(export_folder)
        return zip_file_path

    def __upload_to_s3(self, zip_file_path: str) -> None:
        """Upload zip file to S3."""
        try:
            self.__log_and_print(f"Uploading to S3...")

            # Get zip file name
            zip_file_name = os.path.basename(zip_file_path)

            # S3 object key
            s3_key = f"pdf/{zip_file_name}"

            # Upload to S3
            s3 = S3()
            s3.upload_direct_to_s3(zip_file_path, s3_key)

            self.__log_and_print(f"Successfully uploaded to S3: {s3_key}")

        except Exception as e:
            self.__log_and_print(f"Error uploading to S3: {str(e)}")

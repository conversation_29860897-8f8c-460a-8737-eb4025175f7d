import logging
import os
import zipfile
from datetime import datetime
from pathlib import Path

import requests
from django.core.management.base import BaseCommand
from django.db.models import QuerySet

from companies.models import Company
from diagnostics.models import Diagnostics
from files.services import S3
from management_sheets.models import ManagementSheet
from warranty.models import Warranty

logger = logging.getLogger(__name__)


def _now_folder_name() -> str:
    """Generate a timestamped folder name."""
    return datetime.now().strftime("export_%Y%m%d_%H%M")


def _ensure_dir(path: Path) -> Path:
    """Create directory and all parents. Return the path."""
    path.mkdir(parents=True, exist_ok=True)
    return path


def _safe_company_folder_name(company: Company) -> str:
    """Generate a safe folder name from company name."""
    name = (getattr(company, "name", None) or "company").strip().replace(" ", "_")
    return f"{company.id}_{name}"


def _download_url_to_path(url: str, dest: Path) -> bool:
    """Download file from URL and save to destination. Return True if successful."""
    try:
        resp = requests.get(url, stream=True, timeout=30)
        resp.raise_for_status()
        with open(dest, "wb") as fh:
            for chunk in resp.iter_content(1024 * 8):
                if chunk:
                    fh.write(chunk)
        return True
    except Exception:
        logger.exception("Failed to download url: %s", url)
        return False


def _zip_folder(folder: Path) -> Path:
    """Create a zip file from folder contents. Return zip file path."""
    zip_name = f"{folder.name}.zip"
    zip_path = folder.parent / zip_name
    with zipfile.ZipFile(zip_path, "w", compression=zipfile.ZIP_DEFLATED) as zf:
        for root, _, files in os.walk(folder):
            for f in files:
                abs_path = Path(root) / f
                arcname = abs_path.relative_to(folder.parent)
                zf.write(abs_path, arcname)
    return zip_path


class Command(BaseCommand):
    help = "Export PDFs for companies and optionally upload a zip to S3"

    def add_arguments(self, parser):
        parser.add_argument(
            "--type",
            dest="type",
            choices=["warranty", "management_sheet", "diagnostics"],
            help="One of warranty, management_sheet, diagnostics",
        )
        parser.add_argument(
            "--company",
            dest="company",
            type=int,
            help="Company ID to filter",
        )
        parser.add_argument(
            "--upload_to_s3",
            dest="upload_to_s3",
            action="store_true",
            help="Upload resulting zip to S3",
        )

    def _log(self, message: str, level: str = "info") -> None:
        """Log message and write to stderr."""
        getattr(logger, level)(message)
        self.stderr.write(message)

    def _get_companies(self, company_id: int | None) -> list[Company]:
        """Get companies. If company_id provided, filter by it. Return empty list if not found."""
        if company_id:
            try:
                comp = Company.objects.get(id=company_id)
                return [comp]
            except Company.DoesNotExist:
                self._log(f"Company id={company_id} not found", "error")
                return []
        return list(Company.objects.all())

    def _get_model_for_type(self, type_name: str):
        """Map type name to ORM model."""
        mapping = {
            "warranty": Warranty,
            "management_sheet": ManagementSheet,
            "diagnostics": Diagnostics,
        }
        return mapping[type_name]

    def _query_objects_for_type(self, type_name: str, company: Company) -> QuerySet:
        """Query objects by type and company with non-null pdf_url."""
        model = self._get_model_for_type(type_name)
        return model.objects.filter(company=company, pdf_url__isnull=False)

    def _download_pdf(
        self, pdf_url: str, type_dir: Path, filename: str
    ) -> bool:
        """Download single PDF to type directory. Return True if successful."""
        if not pdf_url:
            return False
        dest = type_dir / filename
        return _download_url_to_path(pdf_url, dest)

    def _download_pdfs_for_type(
        self, type_name: str, company: Company, company_dir: Path
    ) -> int:
        """Download all PDFs for a type. Return count of files copied."""
        type_dir = _ensure_dir(company_dir / type_name)
        qs = self._query_objects_for_type(type_name, company)
        count = 0

        for obj in qs:
            pdf_url = getattr(obj, "pdf_url", None)
            if not pdf_url:
                continue
            filename = Path(pdf_url).name
            if self._download_pdf(pdf_url, type_dir, filename):
                count += 1

        self._log(f"Copied {count} files for company={company.id} type={type_name}")
        return count

    def _process_company(
        self, company: Company, export_root: Path, type_names: list[str]
    ) -> int:
        """Process single company. Download PDFs for all types. Return total count."""
        company_folder = _ensure_dir(
            export_root / _safe_company_folder_name(company)
        )
        total = 0

        for type_name in type_names:
            try:
                copied = self._download_pdfs_for_type(
                    type_name, company, company_folder
                )
                total += copied
            except Exception:
                logger.exception(
                    "Error downloading PDFs for company=%s type=%s",
                    company.id,
                    type_name,
                )

        self._log(f"Total files copied for company={company.id}: {total}")
        return total

    def _upload_zip_to_s3(self, zip_path: Path) -> None:
        """Upload zip file to S3."""
        s3 = S3()
        object_name = f"pdf/{zip_path.name}"
        self._log(f"Uploading {zip_path} to s3://{s3.bucket_name}/{object_name}")
        s3.upload_direct_to_s3(str(zip_path), object_name=object_name)

    def handle(self, *args, **options):
        type_opt = options.get("type")
        company_id = options.get("company")
        upload_to_s3 = options.get("upload_to_s3")

        # Create export folder
        export_root = Path.cwd() / _now_folder_name()
        _ensure_dir(export_root)
        self._log(f"Created export folder: {export_root}")

        # Get companies to process
        companies = self._get_companies(company_id)
        if company_id and not companies:
            return

        # Determine which types to process
        all_type_names = ["warranty", "management_sheet", "diagnostics"]
        type_names = all_type_names if type_opt is None else [type_opt]

        # Process each company
        for company in companies:
            self._process_company(company, export_root, type_names)

        # Create zip file
        zip_path = _zip_folder(export_root)
        self._log(f"Created zip at: {zip_path}")

        # Upload to S3 if requested
        if upload_to_s3:
            try:
                self._upload_zip_to_s3(zip_path)
                self._log("Upload to S3 completed")
            except Exception:
                logger.exception("Failed to upload zip to S3")

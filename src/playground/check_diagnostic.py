from diagnostics.models import Diagnostic

diagnostic: Diagnostic = Diagnostic.objects.filter(pdf_url__isnull=False).order_by("created_at").first()
print(f"Diagnostic ID: {diagnostic.id} with PDF URL: {diagnostic.pdf_url}, created at {diagnostic.created_at}")

from management_sheets.models import ManagementSheet
ms: ManagementSheet = ManagementSheet.objects.filter(pdf_url__isnull=False).order_by("created_at").first()
print(f"ManagementSheet ID: {ms.id} with PDF URL: {ms.pdf_url}, created at {ms.created_at}")

from django.db.models import Q
from kanna_api.models import UserKanna
from diagnostics.models import Diagnostic
from approve_flow.models import (ApproveFlowStep)

diagnostic: Diagnostic = Diagnostic.objects.filter(pk=5347).first()
approve_flow_setting_instance = (
    diagnositc.diagnostic_approve_flow_setting_instance
)
approve_steps = ApproveFlowStep.objects.filter(diagnostic=diagnostic).all()
last_step = approve_steps[0]
kanna_user: UserKanna = last_step.user.kanna_user
print(approve_flow_setting_instance.is_approve_level_act)
company_id = last_step.company.id
print(f"Company ID: {company_id}")

email_act_list = (
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
    "<EMAIL>"
)

kanna_act_list = UserKanna.objects.filter(email__in=email_act_list).all()
for kanna in kanna_act_list:
    print(f"Kanna User: {kanna.email} - Company ID: {kanna.level} -UUID: {kanna.user_uuid}, user_id: {kanna.user_id}")
next_level = "LEVEL_3"


from notification_settings.services import get_list_user_turn_on_notification
recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            company_id=company_id, level=next_level
        )
for user_uuid in recipient_ids:
    print(f"User UUID: {user_uuid}")
recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=diagnostic.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
print(f"Recipient IDs: {recipient_ids}")

cms_uuid = diagnostic.cms_uuid
user_check = UserKanna.objects.all()
for user in user_check:
    if "shunyasumasa" in user.email:
        print(f"User: {user.email} - UUID: {user.id} -- email: {user.email} - level: {user.level} - company_id: {user.company_id}")

step_2 = approve_steps[1]
print(f"Step 2: {step_2.approve_flow_setting_instance} - User: {step_2.user.kanna_user.email} - Level: {step_2.user.kanna_user.level}")
print(f"step 2: {step_2.approve_flow_setting_instance.is_approve_level_act}")

print(approve_flow_setting_instance.dealer)
from authentication.models import User
recipients = User.objects.filter(id__in=recipient_ids).all()
for recipient in recipients:
    print(f"Recipient: {recipient.email} - ID: {recipient.id}")


from notification_settings.models import NotificationEmailSetting
notification_setting = NotificationEmailSetting.objects.filter(
    cms_uuid=cms_uuid,
    user_uuid='cb534970-1fdb-4ea3-a299-7221a7d814e5',
).all()
for setting in notification_setting:
    print(f"Notification Setting: {setting.user_uuid} - Receive Email: {setting.is_receive_email} - Receive Notification: {setting.is_receive_notification} - Assigned: {setting.is_assigned}")

notification_settings = NotificationEmailSetting.objects.filter(
        cms_uuid=cms_uuid, is_assigned=True, is_receive_notification=True
    ).values_list("user_uuid", flat=True)
for notification in notification_settings:
    print(f"Notification User email: {notification}")

user_kanna_uuids=recipient_ids
user_kanna_uuids = [str(user_uuid) for user_uuid in user_kanna_uuids]
notification_settings_str = [str(uuid) for uuid in notification_settings]
print(f"Notification Settings UUIDs: {notification_settings_str}")

users_turn_on_notifications = set(user_kanna_uuids).intersection(
        notification_settings_str
    )
print(f"Users Turn On Notifications: {users_turn_on_notifications}")
user_send_notification_to = UserKanna.objects.filter(user_uuid__in=users_turn_on_notifications)
for user in user_send_notification_to:
    print(f"User to send notification: {user.email} - UUID: {user.user_uuid} - User ID: {user.user_id}")

from notifications.models import Notification

notifications = Notification.objects.filter(
    recipient=1787,
    verb="APPROVE_REQUEST"
).all()

for notification in notifications:
    print(f"Notification: {notification.id} - Recipient: {notification.recipient} - Verb: {notification.verb} - Action Object: {notification.action_object} at {notification.timestamp}")

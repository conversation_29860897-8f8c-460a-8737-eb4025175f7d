from datetime import datetime

from django.apps import apps
from django.db.models import Q
from django.conf import settings
from rest_framework.exceptions import PermissionDenied

from api import constants
from api.constants import LEVEL_MEMBERS, LEVEL_MEMBERS_NUMBER
from approve_flow.models import ApproveFlowStepStatusChoices, UserRoleType
from cms.services import get_cms_kanna
from cms_document.services import (
    get_document_folder_uuid,
    store_document_information,
)
from companies.models import Company, CompanyType
from diagnostics.choices import (
    DiagnosticConditionTypes,
    DiagnosticStatus,
    DiagnosticTypes,
)
from kanna_api.cms_document import create_document
from kanna_api.models.user_kanna import LevelChoices, UserKanna
from template_management.models import (
    BusinessSubTypes,
    BusinessTypes,
    Template,
    TemplateStatus,
)
from template_management.serializers import TemplateSerializer
from utilities.helpers.date_converter import convert_west_to_jp
from utilities.pdf_helper.pdf_handler import render_to_pdf, render_to_pdf_agc
from warranty.models import Warranty, WarrantyYearChoices
from .models import ApproveFlowStep, UserElectronicMark

level_priority_dict = {
    LevelChoices.LEVEL_1.value: 1,
    LevelChoices.LEVEL_2.value: 2,
    LevelChoices.LEVEL_3.value: 3,
}


def query_list_level_in_company(company: Company):
    user_kanna = (
        UserKanna.objects.values_list("level", flat=True)
        .filter(company=company)
        .distinct()
    )

    return user_kanna


def is_exist_lv3_in_list_lv_in_company(company: Company):
    lv3 = LEVEL_MEMBERS["LEVEL_3"]
    list_lv = (
        UserKanna.objects.values_list("level", flat=True)
        .filter(company=company)
        .distinct()
    )
    return lv3 in list_lv

def get_min_max_level_in_company(company: Company):
    list_level_in_company = query_list_level_in_company(company=company)

    level_number = []
    for level in list(list_level_in_company):
        if level == LEVEL_MEMBERS["LEVEL_1"]:
            level_number.append(LEVEL_MEMBERS_NUMBER["LEVEL_1"])
        elif level == LEVEL_MEMBERS["LEVEL_2"]:
            level_number.append(LEVEL_MEMBERS_NUMBER["LEVEL_2"])
        elif level == LEVEL_MEMBERS["LEVEL_3"]:
            level_number.append(LEVEL_MEMBERS_NUMBER["LEVEL_3"])

    level_number.sort()
    level_number_min = level_number[0]
    level_number_max = level_number[len(level_number) - 1]

    level = {
        LEVEL_MEMBERS_NUMBER["LEVEL_1"]: LEVEL_MEMBERS["LEVEL_1"],
        LEVEL_MEMBERS_NUMBER["LEVEL_3"]: LEVEL_MEMBERS["LEVEL_3"],
    }
    if company.type == CompanyType.DEALER:
        level[LEVEL_MEMBERS_NUMBER["LEVEL_2"]] = LEVEL_MEMBERS["LEVEL_2"]
    min_level = level.get(level_number_min, None)
    max_level = level.get(level_number_max, None)

    # if ACT lv2,lv3 => min_level = max_level = 3
    # if ACT lv1,lv2 => min_level = max_level = 1
    min_level = min_level or max_level
    max_level = max_level or min_level
    return min_level, max_level


def get_next_level_by_priority(level, list_level_in_company):
    temp = list(level_priority_dict)
    try:
        next_level = temp[temp.index(level) + 1]
        if next_level in list_level_in_company:
            return next_level
        else:
            return get_next_level_by_priority(next_level, list_level_in_company)
    except (ValueError, IndexError):
        return None


def get_next_level(user: UserKanna):
    list_level_in_company = query_list_level_in_company(company=user.company).all()
    next_level = get_next_level_by_priority(user.level, list_level_in_company)
    return next_level


def get_previous_level_by_priority(level, list_level_in_company):
    list_level_in_company = [str(level) for level in list_level_in_company]
    levels_in_priority_order = sorted(list_level_in_company)
    index_of_level = levels_in_priority_order.index(level)

    if index_of_level == 0:
        return None

    previous_level = levels_in_priority_order[:index_of_level]
    return previous_level


def get_previous_level(user: UserKanna):
    list_level_in_company = query_list_level_in_company(company=user.company).all()
    previous_level = get_previous_level_by_priority(user.level, list_level_in_company)
    return previous_level


def query_current_approve_flow_step(kanna_user: UserKanna, diagnostic):
    query = ApproveFlowStep.objects.filter(
        diagnostic=diagnostic,
        approve_flow_setting_instance=diagnostic.diagnostic_approve_flow_setting_instance,
        company=kanna_user.company,
    )
    return query


def query_current_approve_flow_step_from_admin(diagnostic):
    query = ApproveFlowStep.objects.filter(
        diagnostic=diagnostic,
        approve_flow_setting_instance=diagnostic.diagnostic_approve_flow_setting_instance,
        user=None,
    ).order_by("-id").first()
    return query


def query_current_approve_flow_step_from_admin_ms(management_sheet):
    query = ApproveFlowStep.objects.filter(
        management_sheet=management_sheet,
        approve_flow_setting_instance=management_sheet.management_sheet_approve_flow_setting_instance,
        user=None,
    ).order_by("-id").first()
    return query


def query_current_approve_flow_step_ms(kanna_user: UserKanna, management_sheet):
    query = ApproveFlowStep.objects.filter(
        management_sheet=management_sheet,
        approve_flow_setting_instance=management_sheet.management_sheet_approve_flow_setting_instance,
        company=kanna_user.company,
    )
    return query


# Get current approve flow step without level
def get_current_approve_flow_step_without_level(kanna_user: UserKanna, diagnostic):
    current_step = query_current_approve_flow_step(kanna_user, diagnostic).first()
    return current_step


def get_current_approve_flow_step_without_level_ms(
    kanna_user: UserKanna, management_sheet
):
    current_step = query_current_approve_flow_step_ms(
        kanna_user, management_sheet
    ).first()
    return current_step


# Get current approve flow step with level
def get_current_apprve_flow_step(kanna_user: UserKanna, diagnostic):
    current_step = (
        query_current_approve_flow_step(kanna_user, diagnostic)
        .filter(level=kanna_user.level)
        .first()
    )
    return current_step


def get_current_apprve_flow_step_ms(kanna_user: UserKanna, management_sheet):
    current_step = (
        query_current_approve_flow_step_ms(kanna_user, management_sheet)
        .filter(level=kanna_user.level)
        .first()
    )
    return current_step


def get_template_data(business_type, sub_type=None):
    template_management_query = Template.objects.filter(
        business_type=business_type,
        status=TemplateStatus.ACTIVE,
    )
    if sub_type is not None:
        template_management_query = template_management_query.filter(sub_type=sub_type)
    template_management = template_management_query.first()

    template_title = ""
    template_enterprise_name = "AGCコーテック"
    template_shop_name = ""
    template_logo = ""
    if template_management is not None:
        template_management_data_sr = TemplateSerializer(template_management).data
        if template_management_data_sr["extra_fields"] is not None:
            for extra_field in template_management_data_sr["extra_fields"]:
                if extra_field["key"] == "title":
                    template_title = extra_field["value"]
                if extra_field["key"] == "enterprise_name" and extra_field["value"]:
                    template_enterprise_name = extra_field["value"]
                if extra_field["key"] == "shop_name":
                    template_shop_name = extra_field["value"]
                if extra_field["key"] == "logo":
                    template_logo = extra_field["value"]
    return template_title, template_enterprise_name, template_shop_name, template_logo


# Create new approve flow step
def create_new_approve_flow_step_instace(
    kanna_user: UserKanna, diagnostic, status, comment
):
    user_company_min_level, _ = get_min_max_level_in_company(kanna_user)
    new_approve_flow_step = ApproveFlowStep(
        approve_flow_setting_instance=diagnostic.diagnostic_approve_flow_setting_instance,
        diagnostic=diagnostic,
        user=None,
        level=user_company_min_level,
        status=status,
        comment=comment,
        company=kanna_user.company,
    )
    new_approve_flow_step.save()
    return new_approve_flow_step


def create_new_approve_flow_step_instace_ms(
    kanna_user: UserKanna, management_sheet, status, comment
):
    user_company_min_level, _ = get_min_max_level_in_company(kanna_user)
    new_approve_flow_step = ApproveFlowStep(
        approve_flow_setting_instance=management_sheet.management_sheet_approve_flow_setting_instance,
        management_sheet=management_sheet,
        user=None,
        level=user_company_min_level,
        status=status,
        comment=comment,
        company=kanna_user.company,
    )
    new_approve_flow_step.save()
    return new_approve_flow_step


# Function support states
def get_last_step(approve_flow_setting_instance, kanna_user, diagnostic_instance):
    if approve_flow_setting_instance.is_approve_level_dealer:
        last_step = get_current_apprve_flow_step(kanna_user, diagnostic_instance)
    else:
        last_step = get_current_approve_flow_step_without_level(
            kanna_user, diagnostic_instance
        )
    return last_step


def get_last_step_ms(approve_flow_setting_instance, kanna_user, management_sheet):
    if approve_flow_setting_instance.is_approve_level_dealer:
        last_step = get_current_apprve_flow_step_ms(kanna_user, management_sheet)
    else:
        last_step = get_current_approve_flow_step_without_level_ms(
            kanna_user, management_sheet
        )
    return last_step


def handle_return_reject_sds(
    last_step,
    kanna_user,
    diagnostic_instance,
    step_status,
    diagnostic_status,
    comment="",
):
    if last_step:
        # Update last Approve Flow Step
        last_step.status = step_status
        last_step.user = kanna_user.user
        last_step.comment = comment
        last_step.save()

        # Update diagnostic
        diagnostic_instance.status = diagnostic_status
        diagnostic_instance.save()
    else:
        raise PermissionDenied({"detail": "Invalid action"})


def handle_return_reject_ms(
    last_step,
    kanna_user,
    management_sheet,
    step_status,
    management_sheet_status,
    comment="",
):
    if last_step:
        # Update last Approve Flow Step
        last_step.status = step_status
        last_step.user = kanna_user.user
        last_step.comment = comment
        last_step.save()

        # Update diagnostic
        management_sheet.status = management_sheet_status
        management_sheet.save()
    else:
        raise PermissionDenied({"detail": "Invalid action"})


# Warranty
def get_all_construction_methods_in_ms(management_sheet):
    # Avoid circula model import
    diagnostic_model = apps.get_model("diagnostics", "Diagnostic")
    diagnostics = (
        diagnostic_model.objects.filter(management_sheet=management_sheet)
        .only("type", "construction_method")
        .exclude(status=DiagnosticStatus.REJECT)
        .order_by("id")
        .all()
    )
    list_construction_methods = [
        diagnostic.construction_method for diagnostic in diagnostics
    ]
    return list_construction_methods


def prepare_warranty_data_to_pdf(warranty, warranty_coverage, kanna_user):
    management_sheet = warranty.management_sheet
    # owner infomation
    owner_name = management_sheet.client_name
    # @TODO: confirm with client
    construction_no = management_sheet.construction_no
    cms = get_cms_kanna(management_sheet.cms_uuid, kanna_user)
    construction_name = cms.get("title", "")
    address = f"{management_sheet.prefecture}{management_sheet.city}{management_sheet.full_address}"
    # datetime
    warranty_date_start = management_sheet.completed_date
    if warranty.warranty_year_type == WarrantyYearChoices.TEN_YEARS:
        warranty_date_end = management_sheet.ten_years_end_warranty_date
    else:
        warranty_date_end = management_sheet.fifteen_years_end_warranty_date
    user_manual_no = ""
    if management_sheet.warranty_registration_no is not None:
        user_manual_no = management_sheet.warranty_registration_no

    diagnostic = management_sheet.diagnostic_management_sheet.first()
    if diagnostic is not None:
        shop_address = (
            ""
            if diagnostic.company.address is None
            else f"{diagnostic.company.full_address} "
        )
        shop_postcode = (
            "" if diagnostic.company.postcode is None else diagnostic.company.postcode
        )
        shop_name = "" if diagnostic.company.name is None else diagnostic.company.name

    # template
    (
        template_title,
        template_enterprise_name,
        template_shop_name,
        template_logo,
    ) = get_template_data(
        business_type=BusinessTypes.WARRANTY,
    )

    warranty_template = Template.objects.filter(
        business_type=BusinessTypes.WARRANTY,
        status=TemplateStatus.ACTIVE,
    ).first()
    show_residence = warranty_template.get_extra_field_value("show_residence")
    return {
        "user_manual_no": user_manual_no or "",
        "construction_no": construction_no or "",
        "construction_name": construction_name or "",
        "owner_name": owner_name or "",
        "address": address or "",
        "warranty_coverages": warranty_coverage or "",
        "warranty_date_start": warranty_date_start or "",
        "warranty_date_end": warranty_date_end or "",
        "warranty_creation_date": warranty.created_at,
        "shop_address": shop_address or "",
        "shop_postcode": shop_postcode or "",
        "shop_name": shop_name or "",
        # @TODO: Static file
        "api_url": settings.API_URL,
        "template_title": template_title,
        "template_enterprise_name": template_enterprise_name,
        "template_shop_name": template_shop_name,
        "template_logo": template_logo,
        "show_residence": show_residence,
    }


# @TODO: Dummy data
def prepare_diagnostic_data_to_pdf(diagnostic, kanna_user):
    # TODO: fix import circel error
    from diagnostics.serializers import GetDiagnosticDetailSerialzer

    # get total of photos
    total_photos = diagnostic.get_total_photos()
    diagnostic = GetDiagnosticDetailSerialzer(
        diagnostic, context={"user": kanna_user.user}
    ).data
    diagnosis_date = ""
    if diagnostic["diagnosis_date"] is not None:
        diagnosis_date = datetime.fromisoformat(diagnostic["diagnosis_date"])
    application_date = ""
    if diagnostic["application_date"] is not None:
        application_date = datetime.fromisoformat(diagnostic["application_date"])
    confirm_date = ""
    if diagnostic["confirm_date"] is not None:
        confirm_date = datetime.fromisoformat(diagnostic["confirm_date"])
    building_completion_date = ""
    if diagnostic["building_completion_date"] is not None:
        building_completion_date = datetime.fromisoformat(
            diagnostic["building_completion_date"]
        )
        building_completion_date = convert_west_to_jp(building_completion_date)
    condition_substrate = []
    condition_existing_paint_film = []
    condition_other = []
    act_approve_date = ""
    if diagnostic["confirm_date"] is not None:
        act_approve_date = datetime.fromisoformat(diagnostic["confirm_date"])

    for diagnostic_condition in diagnostic["diagnostic_conditions"]:
        if diagnostic_condition["type"] == DiagnosticConditionTypes.SUBSTRATE:
            condition_substrate.append(diagnostic_condition)
        elif (
            diagnostic_condition["type"] == DiagnosticConditionTypes.EXISTING_PAINT_FILM
        ):
            condition_existing_paint_film.append(diagnostic_condition)
        else:
            condition_other.append(diagnostic_condition)

    # convert string type to JP
    diagnosis_type = diagnostic["type"]
    sub_type = ""
    if diagnosis_type == DiagnosticTypes.OUTER_WALL:
        diagnosis_type = constants.SDS_OUTER_WALL
        sub_type = BusinessSubTypes.OUTER_WALL_DIAGNOSTIC
    else:
        diagnosis_type = constants.SDS_ROOF
        sub_type = BusinessSubTypes.ROOF_DIAGNOSTIC

    # template
    (
        template_title,
        template_enterprise_name,
        template_shop_name,
        template_logo,
    ) = get_template_data(business_type=BusinessTypes.DIAGNOSTIC, sub_type=sub_type)
    electronic_mark_shop_base64 = (
        UserElectronicMark.objects.filter(
            diagnostic=diagnostic["id"], user_role_type=UserRoleType.SHOP
        )
        .order_by("id")
        .first()
    )

    electronic_mark_act_or_admin_base64 = (
        UserElectronicMark.objects.filter(
            diagnostic=diagnostic["id"], user_role_type__in=(UserRoleType.ACT, UserRoleType.ADMIN)
        )
        .order_by("id")
        .first()
    )

    if electronic_mark_shop_base64:
        electronic_mark_shop_base64_temp = electronic_mark_shop_base64.image_base64
    else:
        electronic_mark_shop_base64_temp = ""

    electronic_mark_act_or_admin_name = ""
    if electronic_mark_act_or_admin_base64:
        electronic_mark_act_or_admin_base64_temp = (
            electronic_mark_act_or_admin_base64.image_base64
        )
        electronic_mark_act_or_admin_name = (
            electronic_mark_act_or_admin_base64.user.get_full_name()
        )
    else:
        electronic_mark_act_or_admin_base64_temp = ""

    # get construction_name
    cms = get_cms_kanna(diagnostic["cms_uuid"], kanna_user)
    construction_name = cms.get("title", "")
    return {
        "construction_no": diagnostic["cms_uuid"] or "",
        "diagnostic": diagnostic,
        "diagnosis_date": diagnosis_date or "",
        "building_completion_date": building_completion_date or "",
        "act_approve_date": act_approve_date or "",
        # "building_name": diagnostic["building_name"] or "",
        "construction_name": construction_name or "",
        "building_age": diagnostic["building_age"] or "",
        "building_name_furigana": diagnostic["building_name_furigana"] or "",
        "building_structure": diagnostic["building_structure"] or "",
        "building_area": diagnostic["building_area"] or "",
        "owner_name": diagnostic["construction_owner"] or "",
        "address": diagnostic["construction_address_extend"] or "",
        "tel": diagnostic["phone_number"] or "",
        "shop_name": diagnostic["company"]["name"] or "",
        "shop_tel": diagnostic["company"]["tel"] or "",
        "shop_fax": diagnostic["company"]["fax"] or "",
        "pic_name": diagnostic["person"]["full_name"] or "",
        "type": diagnosis_type,
        "condition_substrate": condition_substrate,
        "condition_existing_paint_film": condition_existing_paint_film,
        "condition_other": condition_other,
        "confirm_date": confirm_date or "",
        "application_date": application_date or "",
        "diagnostic_construction_methods": diagnostic["diagnostic_construction_methods"],
        "required_fields": constants.MS_PDF_REQUIRED_FIELDS,
        "electronic_mark_shop_base64": electronic_mark_shop_base64_temp,
        "electronic_mark_act_or_admin_base64": electronic_mark_act_or_admin_base64_temp,
        "electronic_mark_act_or_admin_name": electronic_mark_act_or_admin_name,
        "required_1": constants.PDF_SDS_OUTER_WALL_REQUIRED_FIELDS_1,
        "required_3": constants.SDS_OUTER_WALL_PDF_REQUIRED_FIELDS,
        "ignore_fields": constants.SDS_OUTER_WALL_PDF_IGNORE_FIELDS,
        "total_photos": total_photos,
        "template_title": template_title,
        "template_enterprise_name": template_enterprise_name,
        "template_shop_name": template_shop_name,
        "template_logo": template_logo,
        "api_url": settings.API_URL,
    }


def render_to_pdf_and_send_to_kanna_diagnostic(kanna_user, diagnostic, cms_uuid):
    folder_uuid = get_document_folder_uuid("diagnostic", kanna_user, cms_uuid)

    # Render PDF and send to KANNA
    diagnostic_pdf_data = prepare_diagnostic_data_to_pdf(
        diagnostic=diagnostic, kanna_user=kanna_user
    )

    pdf_file_name = ""
    template_path = ""
    if diagnostic.type == DiagnosticTypes.ROOF:
        template_path = "documents/diagnostic_roof.html"
        pdf_file = render_to_pdf(template_path, diagnostic_pdf_data)
        pdf_file_name = (
            f"DiagnoticRoof_{diagnostic.created_at.strftime('%Y%m%d_%H%M%S')}.pdf"
        )

    if diagnostic.type == DiagnosticTypes.OUTER_WALL:
        template_path = "documents/diagnostic_outer_wall.html"
        pdf_file = render_to_pdf(template_path, diagnostic_pdf_data)
        pdf_file_name = (
            f"DiagnoticOuterwall_{diagnostic.created_at.strftime('%Y%m%d_%H%M%S')}.pdf"
        )

    diagnostic_pdf_agc = render_to_pdf_agc(
        template_path, pdf_file_name, diagnostic_pdf_data
    )
    diagnostic.pdf_url = diagnostic_pdf_agc
    diagnostic.save()

    # Send pdf to KANNA
    result = create_document(
        user=kanna_user,
        cms_uuid=cms_uuid,
        document_category_uuid=folder_uuid,
        files={
            "file": (
                pdf_file_name,
                pdf_file,
                "application/pdf",
            )
        },
    )
    # Store record in DB
    store_document_information(
        image_uuid=result["document"]["uuid"],
        document_uuid=result["document"]["documentCategory"]["uuid"],
        cms_uuid=cms_uuid,
        diagnostic=diagnostic,
    )


def get_full_information_diagnostic_of_ms(management_sheet):
    from management_sheets.models import (
        ActualConstructionMethodType,
    )
    from diagnostics.models import DiagnosticConstructionMethod

    type_values = [choice[0] for choice in ActualConstructionMethodType.choices]
    default_type_value = {"construction_time": "", "material_id": 0, "capacity": 0}

    diagnostics = []
    for diagnostic in management_sheet["diagnostics"]:
        for actual_construction in diagnostic["actual_constructions"]:
            # Get full actual construction method
            existing_methods = {item["type"]: item for item in actual_construction["actual_construction_methods"]}
            actual_construction_methods = [
                existing_methods[type_value] if type_value in existing_methods else default_type_value
                for type_value in type_values
            ]
            actual_construction["actual_construction_methods"] = actual_construction_methods

            # Get construction method spec name
            diagnostic_cm = DiagnosticConstructionMethod.objects.filter(
                id=actual_construction["diagnostic_construction_method_id"]).first()
            actual_construction["cm_spec_name"] = diagnostic_cm.construction_method.spec_name
        diagnostics.append(diagnostic)

    return diagnostics


def prepare_management_sheet_data_to_pdf(kanna_user, management_sheet):
    from management_sheets.serializers import GetManagementSheetDetailSerialzer

    sds = management_sheet.diagnostic_management_sheet.exclude(
        status=DiagnosticStatus.REJECT
    ).first()
    if sds:
        shop_no = sds.company.id
    management_sheet = GetManagementSheetDetailSerialzer(
        management_sheet, context={"user": kanna_user.user}
    ).data
    approve_follow_step = ApproveFlowStep.objects.filter(
        management_sheet_id=management_sheet["id"],
        status=ApproveFlowStepStatusChoices.APPROVED,
    ).first()
    user_submit_approve = None
    approve_request_date = None
    if approve_follow_step:
        user_submit_approve = approve_follow_step.user
        approve_request_date = approve_follow_step.created_at
    # get  electronic mark for SHOP
    electronic_mark_shop_base64 = (
        UserElectronicMark.objects.filter(
            management_sheet=management_sheet["id"], user_role_type=UserRoleType.SHOP
        )
        .order_by("id")
        .first()
    )
    # get  electronic mark for DEALER
    electronic_mark_dealer_base64 = (
        UserElectronicMark.objects.filter(
            management_sheet=management_sheet["id"], user_role_type=UserRoleType.DEALER
        )
        .order_by("id")
        .first()
    )

    is_approved_by_act = True
    # get  electronic mark for ACT
    electronic_mark_base64 = (
        UserElectronicMark.objects.filter(
            management_sheet=management_sheet["id"], user_role_type=UserRoleType.ACT
        )
        .order_by("id")
        .first()
    )

    if electronic_mark_base64 is None:
        # get electronic mark for Admin
        electronic_mark_base64 = (
            UserElectronicMark.objects.filter(
                management_sheet=management_sheet["id"],
                user_role_type=UserRoleType.ADMIN
            )
            .order_by("id")
            .first()
        )
        is_approved_by_act = False

    electronic_mark_shop_name = ""
    electronic_mark_shop_user_name = ""
    if electronic_mark_shop_base64:
        electronic_mark_shop_base64_temp = electronic_mark_shop_base64.image_base64
        electronic_mark_shop_user_name = (
            electronic_mark_shop_base64.user.get_full_name()
        )
        electronic_mark_shop_name = electronic_mark_shop_base64.user.get_company()
    else:
        electronic_mark_shop_base64_temp = ""

    electronic_mark_dealer_user_name = ""
    electronic_mark_dealer_name = ""
    if electronic_mark_dealer_base64:
        electronic_mark_dealer_base64_temp = electronic_mark_dealer_base64.image_base64
        electronic_mark_dealer_user_name = (
            electronic_mark_dealer_base64.user.get_full_name()
        )
        electronic_mark_dealer_name = electronic_mark_dealer_base64.user.get_company()
    else:
        electronic_mark_dealer_base64_temp = ""

    electronic_mark_act_or_admin_name = ""
    electronic_mark_act_or_admin_user_name = ""
    if electronic_mark_base64:
        electronic_mark_base64_temp = electronic_mark_base64.image_base64
        electronic_mark_act_or_admin_user_name = (
            electronic_mark_base64.user.get_full_name()
        )
        electronic_mark_act_or_admin_name = electronic_mark_base64.user.get_company()
    else:
        electronic_mark_base64_temp = ""

    # call api cms get cms info
    cms_kanna = get_cms_kanna(management_sheet["cms_uuid"], kanna_user)
    expect_approve_completed_date = ""
    if management_sheet["expect_approve_completed_date"] is not None:
        expect_approve_completed_date = datetime.fromisoformat(
            management_sheet["expect_approve_completed_date"]
        )

    approved_at = ""
    if management_sheet["approved_at"] is not None:
        approved_at = datetime.fromisoformat(management_sheet["approved_at"])

    # Get full information of list diagnostics
    diagnostics = get_full_information_diagnostic_of_ms(management_sheet)

    ms_type = ""
    if management_sheet["type"] == ["NEW_CONSTRUCTION"]:  # noqa
        ms_type = "新築"
    elif management_sheet["type"] == ["RENOVATION"]:  # noqa
        ms_type = "改修"
    else:
        ms_type = "新築 改修"

    # template
    (
        template_title,
        template_enterprise_name,
        template_shop_name,
        template_logo,
    ) = get_template_data(business_type=BusinessTypes.MANAGEMENT_SHEET)
    return {
        "expect_approve_completed_date": expect_approve_completed_date or "",
        "user_submit_approve": user_submit_approve or "",
        "approve_request_date": approve_request_date or "",
        "ms": management_sheet,
        "ms_type": ms_type,
        "shop_no": shop_no or "",
        "shop": management_sheet["company"],
        "pic": management_sheet["pic"],
        "construction_no": management_sheet["construction_no"] or "",
        "cms_kanna": cms_kanna,
        "approved_at": approved_at or "",
        "electronic_mark_shop_base64": electronic_mark_shop_base64_temp,
        "electronic_mark_shop_name": electronic_mark_shop_name,
        "electronic_mark_dealer_base64": electronic_mark_dealer_base64_temp,
        "electronic_mark_dealer_name": electronic_mark_dealer_name,
        "electronic_mark_base64": electronic_mark_base64_temp,
        "electronic_mark_act_or_admin_name": electronic_mark_act_or_admin_name,
        "electronic_mark_dealer_user_name": electronic_mark_dealer_user_name,
        "electronic_mark_act_or_admin_user_name": electronic_mark_act_or_admin_user_name,
        "is_approved_by_act": is_approved_by_act,
        "electronic_mark_shop_user_name": electronic_mark_shop_user_name,
        "diagnostics": diagnostics,
        "template_title": template_title,
        "template_enterprise_name": template_enterprise_name,
        "template_shop_name": template_shop_name,
        "template_logo": template_logo,
        "api_url": settings.API_URL,
    }


def render_to_pdf_and_send_to_kanna_management_sheet(
    kanna_user, management_sheet, cms_uuid
):
    folder_uuid = get_document_folder_uuid("management_sheet", kanna_user, cms_uuid)

    # Render PDF and send to KANNA
    management_sheet_pdf_data = prepare_management_sheet_data_to_pdf(
        kanna_user=kanna_user, management_sheet=management_sheet
    )
    template_path = "documents/management_sheet.html"
    pdf_file = render_to_pdf(template_path, management_sheet_pdf_data)
    pdf_file_name = (
        f"ManagementSheet_{management_sheet.created_at.strftime('%Y%m%d_%H%M%S')}.pdf"
    )
    management_sheet_pdf_agc = render_to_pdf_agc(
        template_path, pdf_file_name, management_sheet_pdf_data
    )
    management_sheet.pdf_url = management_sheet_pdf_agc
    management_sheet.save()

    # Send pdf to KANNA
    result = create_document(
        user=kanna_user,
        cms_uuid=cms_uuid,
        document_category_uuid=folder_uuid,
        files={
            "file": (
                pdf_file_name,
                pdf_file,
                "application/pdf",
            )
        },
    )
    # Store record in DB
    store_document_information(
        image_uuid=result["document"]["uuid"],
        document_uuid=result["document"]["documentCategory"]["uuid"],
        cms_uuid=cms_uuid,
        management_sheet=management_sheet,
    )


def get_actual_construction_area(sds_type):
    if sds_type == DiagnosticTypes.OUTER_WALL:
        return "外壁"
    elif sds_type == DiagnosticTypes.ROOF:
        return "屋根"
    else:
        return ""


# @TODO: Docstring + Refactor
def render_to_pdf_and_send_to_kanna(
    kanna_user, warranty, actual_constructions, cms_uuid
):
    folder_uuid = get_document_folder_uuid("warranty", kanna_user, cms_uuid)

    # Render PDF
    warranty_coverage = [
        (
            get_actual_construction_area(actual_construction.diagnostic_construction_method.diagnostic.type),
            actual_construction.paint_area,
            actual_construction.diagnostic_construction_method.construction_method.spec_name
        )
        for actual_construction in actual_constructions
        if actual_construction.is_approved_to_release_warranty
    ]
    warranty_pdf_data = prepare_warranty_data_to_pdf(
        warranty=warranty, warranty_coverage=warranty_coverage, kanna_user=kanna_user
    )
    pdf_file = render_to_pdf(
        "documents/warranty.html", warranty_pdf_data, page_size="A5", dpi=300
    )
    # Send pdf to KANNA
    result = create_document(
        user=kanna_user,
        cms_uuid=cms_uuid,
        document_category_uuid=folder_uuid,
        files={
            "file": (
                f"WarrantyNo_{warranty.created_at.strftime('%Y%m%d_%H%M%S')}.pdf",
                pdf_file,
                "application/pdf",
            )
        },
    )
    # Store record in DB
    store_document_information(
        image_uuid=result["document"]["uuid"],
        document_uuid=result["document"]["documentCategory"]["uuid"],
        cms_uuid=cms_uuid,
        warranty=warranty,
    )


def render_pdf_to_agc(kanna_user, warranty, actual_constructions, cms_uuid):
    warranty_coverage = [
        (
            get_actual_construction_area(actual_construction.diagnostic_construction_method.diagnostic.type),
            actual_construction.paint_area,
            actual_construction.diagnostic_construction_method.construction_method.spec_name
        )
        for actual_construction in actual_constructions
        if actual_construction.is_approved_to_release_warranty
    ]
    warranty_pdf_data = prepare_warranty_data_to_pdf(
        warranty=warranty, warranty_coverage=warranty_coverage, kanna_user=kanna_user
    )
    file_name = f"WarrantyNo_{warranty.created_at.strftime('%Y%m%d_%H%M%S')}.pdf"
    path_pdf = render_to_pdf_agc(
        "documents/warranty.html", file_name, warranty_pdf_data, page_size="A5", dpi=300
    )
    return path_pdf


def create_warranty_instances(management_sheet, kanna_user, owner=None):
    actual_constructions_ten_years_warranty = (
        management_sheet.actual_constructions.filter(
            Q(
                diagnostic_construction_method__construction_method__warranty_year_type=WarrantyYearChoices.TEN_YEARS,
            )
            | Q(
                diagnostic_construction_method__construction_method__warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
                is_change_15_years_to_10_years=True,
            ),
            is_approved_to_release_warranty=True,
        )
        .select_related("diagnostic_construction_method")
        .order_by("id")
    )
    actual_constructions_fifteen_years_warranty = (
        management_sheet.actual_constructions.filter(
            diagnostic_construction_method__construction_method__warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
            is_approved_to_release_warranty=True,
            is_change_15_years_to_10_years=False,
        )
        .select_related("diagnostic_construction_method")
        .order_by("id")
    )

    if len(actual_constructions_ten_years_warranty) > 0:

        # Create warranty instance
        warranty_ten_year = Warranty(
            management_sheet=management_sheet,
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
            owner=owner,
        )
        warranty_ten_year.save()

        try:
            warranty_url = render_pdf_to_agc(
                kanna_user,
                warranty_ten_year,
                actual_constructions_ten_years_warranty,
                management_sheet.cms_uuid,
            )
            warranty_ten_year.pdf_url = warranty_url
            warranty_ten_year.save()
        except Exception:
            pass

        render_to_pdf_and_send_to_kanna(
            kanna_user,
            warranty_ten_year,
            actual_constructions_ten_years_warranty,
            management_sheet.cms_uuid,
        )

    if len(actual_constructions_fifteen_years_warranty) > 0:

        warranty_fifteen_year = Warranty(
            management_sheet=management_sheet,
            warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
            owner=owner,
        )
        warranty_fifteen_year.save()

        try:
            warranty_url = render_pdf_to_agc(
                kanna_user,
                warranty_fifteen_year,
                actual_constructions_fifteen_years_warranty,
                management_sheet.cms_uuid,
            )
            warranty_fifteen_year.pdf_url = warranty_url
            warranty_fifteen_year.save()
        except Exception:
            pass

        render_to_pdf_and_send_to_kanna(
            kanna_user,
            warranty_fifteen_year,
            actual_constructions_fifteen_years_warranty,
            management_sheet.cms_uuid,
        )

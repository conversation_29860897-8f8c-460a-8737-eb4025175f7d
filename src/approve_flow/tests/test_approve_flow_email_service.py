from unittest.mock import MagicMock, patch

from faker import Faker

from django.core.management import call_command
from django.test import TestCase

from api.constants import KANNA_USER_ROLE, LEVEL_MEMBERS
from approve_flow.approve_flow_email_service import ApproveFlowEmailService
from kanna_api.models import Role, UserKanna
from kanna_api.tests.factories import (
    ActUserKannaFactory,
    AdminUserKannaFactory,
)


class TestApproveFlowEmailService(TestCase):
    @classmethod
    def setUpTestData(cls):
        call_command("loaddata", "fixtures/01-master/00-role.json")

    def setUp(self):
        self.fake = Faker()
        self.role_act = Role.objects.get(id=KANNA_USER_ROLE["ACT"])
        self.role_admin = Role.objects.get(id=KANNA_USER_ROLE["ADMIN_SYSTEM"])

        self.user1 = ActUserKannaFactory(
            role=self.role_act, level=LEVEL_MEMBERS["LEVEL_1"]
        )
        self.user2 = ActUserKannaFactory(
            role=self.role_act, level=LEVEL_MEMBERS["LEVEL_1"]
        )
        self.user3 = ActUserKannaFactory(
            role=self.role_act, level=LEVEL_MEMBERS["LEVEL_3"]
        )

        self.admin_user1: UserKanna = AdminUserKannaFactory(role=self.role_admin)
        self.admin_user2: UserKanna = AdminUserKannaFactory(role=self.role_admin)

        self.kanna_user: UserKanna = ActUserKannaFactory(
            role=self.role_act, level=LEVEL_MEMBERS["LEVEL_1"]
        )

        self.diagnostic_instance = MagicMock()
        self.diagnostic_instance.cms_uuid = self.fake.uuid4()
        self.diagnostic_instance.type = "test-type"

        self.management_sheet = MagicMock()
        self.management_sheet.cms_uuid = self.fake.uuid4()

        self.approve_flow_setting_instance = MagicMock()
        self.approve_flow_setting_instance.act = "test-act"

    def test_get_act_users_at_level_with_email_enabled_no_users(self):
        test_cms_uuid = self.fake.uuid4()

        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.return_value = []

            result = ApproveFlowEmailService.get_act_users_at_level_with_email_enabled(
                next_level=LEVEL_MEMBERS["LEVEL_3"],
                cms_uuid=test_cms_uuid,
            )

            self.assertEqual(result, [])

    def test_send_email_to_act_next_level_sds_success(self):
        user_uuid_1 = self.fake.uuid4()
        user_uuid_2 = self.fake.uuid4()

        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users, patch(
            "approve_flow.approve_flow_email_service.send_approved_approve_flow_sds"
        ) as mock_send_email:

            mock_get_users.return_value = [user_uuid_1, user_uuid_2]
            mock_send_email.return_value = True

            result = ApproveFlowEmailService.send_email_to_act_next_level_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level=LEVEL_MEMBERS["LEVEL_1"],
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertTrue(result)

            mock_send_email.assert_called_once_with(
                company_to="test-act",
                user_approved=self.kanna_user,
                user_to=[user_uuid_1, user_uuid_2],
                comment="Test comment",
                sds_type="test-type",
                cms_title="Test CMS",
            )

    def test_send_email_to_act_next_level_sds_no_users(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.return_value = []

            result = ApproveFlowEmailService.send_email_to_act_next_level_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level=LEVEL_MEMBERS["LEVEL_1"],
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_act_next_level_sds_exception(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.side_effect = Exception("Database error")

            result = ApproveFlowEmailService.send_email_to_act_next_level_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level=LEVEL_MEMBERS["LEVEL_1"],
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_act_next_level_ms_success(self):
        user_uuid_1 = self.fake.uuid4()
        user_uuid_2 = self.fake.uuid4()

        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users, patch(
            "approve_flow.approve_flow_email_service.send_approved_approve_flow_ms"
        ) as mock_send_email:

            mock_get_users.return_value = [user_uuid_1, user_uuid_2]
            mock_send_email.return_value = True

            result = ApproveFlowEmailService.send_email_to_act_next_level_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level=LEVEL_MEMBERS["LEVEL_1"],
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertTrue(result)

            mock_send_email.assert_called_once_with(
                company_to="test-act",
                user_approved=self.kanna_user,
                user_to=[user_uuid_1, user_uuid_2],
                comment="Test comment",
                cms_title="Test CMS",
            )

    def test_send_email_to_act_next_level_ms_no_users(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.return_value = []

            result = ApproveFlowEmailService.send_email_to_act_next_level_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level=LEVEL_MEMBERS["LEVEL_1"],
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_act_next_level_ms_exception(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.side_effect = Exception("Database error")

            result = ApproveFlowEmailService.send_email_to_act_next_level_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level=LEVEL_MEMBERS["LEVEL_1"],
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_admin_sds_success(self):
        admin_email_1 = self.admin_user1.email
        admin_email_2 = self.admin_user2.email

        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users, patch(
            "approve_flow.approve_flow_email_service.send_approved_approve_flow_sds"
        ) as mock_send_email:

            mock_get_users.return_value = [admin_email_1, admin_email_2]
            mock_send_email.return_value = True

            result = ApproveFlowEmailService.send_email_to_admin_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertTrue(result)

            mock_send_email.assert_called_once_with(
                company_to="test-act",
                user_approved=self.kanna_user,
                user_to=[admin_email_1, admin_email_2],
                comment="Test comment",
                sds_type="test-type",
                cms_title="Test CMS",
            )

    def test_send_email_to_admin_sds_no_users(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.return_value = []

            result = ApproveFlowEmailService.send_email_to_admin_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_admin_sds_exception(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.side_effect = Exception("Database error")

            result = ApproveFlowEmailService.send_email_to_admin_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_admin_ms_success(self):
        admin_email_1 = self.admin_user1.email
        admin_email_2 = self.admin_user2.email

        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users, patch(
            "approve_flow.approve_flow_email_service.send_approved_approve_flow_ms"
        ) as mock_send_email:

            mock_get_users.return_value = [admin_email_1, admin_email_2]
            mock_send_email.return_value = True

            result = ApproveFlowEmailService.send_email_to_admin_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertTrue(result)

            mock_send_email.assert_called_once_with(
                company_to="test-act",
                user_approved=self.kanna_user,
                user_to=[admin_email_1, admin_email_2],
                comment="Test comment",
                cms_title="Test CMS",
            )

    def test_send_email_to_admin_ms_no_users(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.return_value = []

            result = ApproveFlowEmailService.send_email_to_admin_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)

    def test_send_email_to_admin_ms_exception(self):
        with patch(
            "approve_flow.approve_flow_email_service.get_list_user_turn_on_email"
        ) as mock_get_users:
            mock_get_users.side_effect = Exception("Database error")

            result = ApproveFlowEmailService.send_email_to_admin_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                comment="Test comment",
                cms_title="Test CMS",
            )

            self.assertFalse(result)
